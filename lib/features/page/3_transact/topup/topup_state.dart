part of 'topup_cubit.dart';

class TopupState extends Equatable {
  final List<TopUpListEntity> topUpList;
  final TopUpListEntity? selectedPayWay;
  final TopUpListPayTypeList? selectedPayType;
  final String input;
  final bool isInputEnabled;
  final NetState netState;
  final num? giftTips; // 充值送xx%

  const TopupState({
    this.topUpList = const [],
    this.selectedPayWay,
    this.selectedPayType,
    this.input = '',
    this.isInputEnabled = true,
    this.netState = NetState.idle,
    this.giftTips,
  });

  TopupState copyWith({
    List<TopUpListEntity>? topUpList,
    TopUpListEntity? selectedPayWay,
    TopUpListPayTypeList? selectedPayType,
    String? input,
    bool? isInputEnabled,
    NetState? netState,
    num? giftTips,
  }) {
    return TopupState(
      topUpList: topUpList ?? this.topUpList,
      selectedPayWay: selectedPayWay ?? this.selectedPayWay,
      selectedPayType: selectedPayType ?? this.selectedPayType,
      input: input ?? this.input,
      isInputEnabled: isInputEnabled ?? this.isInputEnabled,
      netState: netState ?? this.netState,
      giftTips: giftTips ?? this.giftTips,
    );
  }

  @override
  List<Object?> get props => [
        topUpList,
        selectedPayWay,
        selectedPayType,
        input,
        isInputEnabled,
        netState,
        giftTips,
      ];
}
